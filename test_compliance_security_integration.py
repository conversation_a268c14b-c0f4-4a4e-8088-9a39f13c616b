"""
Comprehensive Integration Tests for A.T.L.A.S. Compliance & Security Framework
Tests all compliance and security features end-to-end
"""

import asyncio
import pytest
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any

from atlas_compliance_engine import (
    ComplianceEngine, KYCEngine, AMLEngine, AuditTrailManager,
    ComplianceStatus, DocumentType, RiskLevel
)
from atlas_security_manager import (
    SecurityManager, PasswordValidator, TwoFactorAuth, JWTManager,
    RBACManager, SecurityEventMonitor, UserRole, Permission
)
from models import Order, OrderSide, OrderType

class TestComplianceSecurityIntegration:
    """Comprehensive integration tests for compliance and security"""
    
    @pytest.fixture
    async def setup_engines(self):
        """Setup compliance and security engines"""
        compliance = ComplianceEngine()
        security = SecurityManager("test-jwt-secret-key-12345")
        return compliance, security
    
    @pytest.mark.asyncio
    async def test_complete_kyc_workflow(self, setup_engines):
        """Test complete KYC verification workflow"""
        compliance, security = setup_engines
        
        # 1. Initiate KYC process
        kyc_session = await compliance.kyc_engine.initiate_kyc_process(
            user_id="test_user_001",
            jurisdiction="US"
        )
        
        assert kyc_session["status"] == ComplianceStatus.PENDING.value
        assert "government_id" in kyc_session["required_documents"]
        assert "proof_of_address" in kyc_session["required_documents"]
        assert "tax_document" in kyc_session["required_documents"]
        
        # 2. Upload required documents
        documents = []
        for doc_type in [DocumentType.GOVERNMENT_ID, DocumentType.PROOF_OF_ADDRESS, DocumentType.TAX_DOCUMENT]:
            document = await compliance.kyc_engine.upload_document(
                user_id="test_user_001",
                document_type=doc_type,
                file_data=b"fake_document_data",
                filename=f"{doc_type.value}.pdf"
            )
            documents.append(document)
            assert document.verification_status == ComplianceStatus.PENDING
        
        # 3. Verify documents
        for document in documents:
            verified = await compliance.kyc_engine.verify_document(
                document_id=document.id,
                verifier_id="compliance_officer_001",
                approved=True,
                notes="Document verified successfully"
            )
            assert verified == True
        
        print("✅ KYC workflow test passed")
    
    @pytest.mark.asyncio
    async def test_aml_transaction_monitoring(self, setup_engines):
        """Test AML transaction monitoring and alert generation"""
        compliance, security = setup_engines
        
        # Test normal transaction (should not trigger alert)
        normal_transaction = {
            "id": "txn_001",
            "amount": 5000,
            "symbol": "AAPL",
            "type": "market"
        }
        
        alert = await compliance.aml_engine.monitor_transaction(
            user_id="test_user_001",
            transaction=normal_transaction
        )
        assert alert is None  # No alert for normal transaction
        
        # Test high-risk transaction (should trigger alert)
        high_risk_transaction = {
            "id": "txn_002",
            "amount": 50000,  # High amount
            "symbol": "AAPL",
            "type": "market"
        }
        
        alert = await compliance.aml_engine.monitor_transaction(
            user_id="test_user_001",
            transaction=high_risk_transaction
        )
        
        if alert:  # Alert may be generated based on risk score
            assert alert.risk_score >= 0.7
            assert alert.status == ComplianceStatus.UNDER_REVIEW
            assert "suspicious" in alert.description.lower()
        
        print("✅ AML monitoring test passed")
    
    @pytest.mark.asyncio
    async def test_audit_trail_logging(self, setup_engines):
        """Test comprehensive audit trail logging"""
        compliance, security = setup_engines
        
        # Log various types of actions
        actions = [
            {
                "action": "user_login",
                "resource_type": "session",
                "resource_id": "session_001",
                "details": {"username": "test_user", "ip": "***********"}
            },
            {
                "action": "trade_execution",
                "resource_type": "order",
                "resource_id": "order_001",
                "details": {"symbol": "AAPL", "quantity": 100, "amount": 15000}
            },
            {
                "action": "system_configuration_change",
                "resource_type": "config",
                "resource_id": "trading_limits",
                "details": {"old_limit": 10000, "new_limit": 20000}
            }
        ]
        
        audit_entries = []
        for action_data in actions:
            entry_id = await compliance.audit_manager.log_action(
                user_id="test_user_001",
                action=action_data["action"],
                resource_type=action_data["resource_type"],
                resource_id=action_data["resource_id"],
                details=action_data["details"],
                ip_address="***********",
                user_agent="Mozilla/5.0 Test Browser",
                session_id="session_001"
            )
            audit_entries.append(entry_id)
        
        assert len(audit_entries) == 3
        assert all(entry_id for entry_id in audit_entries)
        
        # Verify audit entries are stored
        assert len(compliance.audit_manager.audit_entries) >= 3
        
        print("✅ Audit trail logging test passed")
    
    @pytest.mark.asyncio
    async def test_authentication_workflow(self, setup_engines):
        """Test complete authentication workflow with 2FA"""
        compliance, security = setup_engines
        
        # Test password validation
        valid, errors = security.password_validator.validate_password("SecurePassword123!")
        assert valid == True
        assert len(errors) == 0
        
        invalid, errors = security.password_validator.validate_password("weak")
        assert invalid == False
        assert len(errors) > 0
        
        # Test 2FA setup
        secret = security.two_factor_auth.generate_secret("test_user_001")
        assert len(secret) == 32  # Base32 secret length
        
        qr_code = security.two_factor_auth.generate_qr_code("test_user_001", secret)
        assert len(qr_code) > 0  # QR code generated
        
        # Test authentication without 2FA
        auth_result = await security.authenticate_user(
            username="test_user",
            password="SecurePassword123!",
            ip_address="***********"
        )
        assert auth_result.success == True
        assert auth_result.access_token is not None
        assert auth_result.refresh_token is not None
        
        print("✅ Authentication workflow test passed")
    
    @pytest.mark.asyncio
    async def test_authorization_rbac(self, setup_engines):
        """Test role-based access control authorization"""
        compliance, security = setup_engines
        
        # Test role permissions
        trader_permissions = security.rbac_manager.get_permissions([UserRole.TRADER])
        assert Permission.EXECUTE_TRADES in trader_permissions
        assert Permission.VIEW_PORTFOLIO in trader_permissions
        assert Permission.MANAGE_USERS not in trader_permissions
        
        viewer_permissions = security.rbac_manager.get_permissions([UserRole.VIEWER])
        assert Permission.EXECUTE_TRADES not in viewer_permissions
        assert Permission.VIEW_PORTFOLIO in viewer_permissions
        assert Permission.VIEW_ANALYTICS in viewer_permissions
        
        admin_permissions = security.rbac_manager.get_permissions([UserRole.ADMIN])
        assert Permission.EXECUTE_TRADES in admin_permissions
        assert Permission.MANAGE_USERS in admin_permissions
        assert Permission.SYSTEM_CONFIGURATION in admin_permissions
        
        # Test permission checking
        assert security.rbac_manager.has_permission([UserRole.TRADER], Permission.EXECUTE_TRADES)
        assert not security.rbac_manager.has_permission([UserRole.VIEWER], Permission.EXECUTE_TRADES)
        assert security.rbac_manager.has_permission([UserRole.ADMIN], Permission.MANAGE_USERS)
        
        print("✅ RBAC authorization test passed")
    
    @pytest.mark.asyncio
    async def test_security_event_monitoring(self, setup_engines):
        """Test security event monitoring and threat detection"""
        compliance, security = setup_engines
        
        # Log multiple failed login attempts (should trigger brute force detection)
        for i in range(6):  # Exceed threshold of 5
            await security.security_monitor.log_security_event(
                event_type="failed_login",
                severity="warning",
                user_id="test_user_001",
                ip_address="***********00",
                description=f"Failed login attempt {i+1}",
                details={"username": "test_user", "attempt": i+1}
            )
        
        # Check if brute force alert was generated
        brute_force_events = [
            event for event in security.security_monitor.events
            if event.event_type == "brute_force_detected"
        ]
        assert len(brute_force_events) >= 1
        
        # Test rate limiting
        rate_limit_ok = await security.check_rate_limit("test_user_001", "***********")
        assert rate_limit_ok == True
        
        print("✅ Security event monitoring test passed")
    
    @pytest.mark.asyncio
    async def test_trading_compliance_integration(self, setup_engines):
        """Test trading compliance checks integration"""
        compliance, security = setup_engines
        
        # Create test order
        test_order = Order(
            id="order_test_001",
            symbol="AAPL",
            quantity=100,
            side=OrderSide.BUY,
            type=OrderType.MARKET,
            price=150.0,
            timestamp=datetime.now()
        )
        
        # Test compliance check
        compliant, message = await compliance.check_trading_compliance(
            user_id="test_user_001",
            order=test_order
        )
        
        assert compliant == True
        assert "passed" in message.lower()
        
        # Test order that exceeds limits
        large_order = Order(
            id="order_test_002",
            symbol="AAPL",
            quantity=1000,
            side=OrderSide.BUY,
            type=OrderType.MARKET,
            price=150.0,
            timestamp=datetime.now()
        )
        
        # This might fail due to daily limits
        compliant, message = await compliance.check_trading_compliance(
            user_id="test_user_001",
            order=large_order
        )
        
        # Result depends on current daily volume
        print(f"Large order compliance: {compliant}, message: {message}")
        
        print("✅ Trading compliance integration test passed")
    
    @pytest.mark.asyncio
    async def test_compliance_reporting(self, setup_engines):
        """Test compliance reporting generation"""
        compliance, security = setup_engines
        
        # Generate compliance report
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        report = await compliance.generate_compliance_report(start_date, end_date)
        
        assert "report_id" in report
        assert "period" in report
        assert "kyc_statistics" in report
        assert "aml_statistics" in report
        assert "audit_statistics" in report
        assert "regulatory_violations" in report
        
        # Verify report structure
        assert report["kyc_statistics"]["total_applications"] > 0
        assert report["aml_statistics"]["total_transactions_monitored"] > 0
        assert report["audit_statistics"]["total_actions_logged"] > 0
        assert isinstance(report["regulatory_violations"], list)
        
        print("✅ Compliance reporting test passed")
    
    @pytest.mark.asyncio
    async def test_end_to_end_compliance_workflow(self, setup_engines):
        """Test complete end-to-end compliance workflow"""
        compliance, security = setup_engines
        
        print("🚀 Starting end-to-end compliance workflow test...")
        
        # 1. User registration and authentication
        auth_result = await security.authenticate_user(
            username="institutional_user",
            password="InstitutionalPassword123!",
            ip_address="***********"
        )
        assert auth_result.success == True
        
        # 2. KYC verification
        kyc_session = await compliance.kyc_engine.initiate_kyc_process(
            user_id="institutional_user_001",
            jurisdiction="US"
        )
        assert kyc_session["status"] == ComplianceStatus.PENDING.value
        
        # 3. Document upload and verification
        document = await compliance.kyc_engine.upload_document(
            user_id="institutional_user_001",
            document_type=DocumentType.GOVERNMENT_ID,
            file_data=b"institutional_id_document",
            filename="institutional_id.pdf"
        )
        
        verified = await compliance.kyc_engine.verify_document(
            document_id=document.id,
            verifier_id="compliance_officer_001",
            approved=True,
            notes="Institutional client verified"
        )
        assert verified == True
        
        # 4. Trading compliance check
        institutional_order = Order(
            id="institutional_order_001",
            symbol="SPY",
            quantity=10000,
            side=OrderSide.BUY,
            type=OrderType.MARKET,
            price=400.0,
            timestamp=datetime.now()
        )
        
        compliant, message = await compliance.check_trading_compliance(
            user_id="institutional_user_001",
            order=institutional_order
        )
        
        # 5. AML monitoring
        transaction_data = {
            "id": institutional_order.id,
            "amount": institutional_order.quantity * institutional_order.price,
            "symbol": institutional_order.symbol,
            "type": institutional_order.type.value
        }
        
        aml_alert = await compliance.aml_engine.monitor_transaction(
            user_id="institutional_user_001",
            transaction=transaction_data
        )
        
        # 6. Audit trail logging
        audit_id = await compliance.audit_manager.log_action(
            user_id="institutional_user_001",
            action="institutional_trade_execution",
            resource_type="order",
            resource_id=institutional_order.id,
            details={
                "order_details": {
                    "symbol": institutional_order.symbol,
                    "quantity": institutional_order.quantity,
                    "amount": institutional_order.quantity * institutional_order.price
                },
                "compliance_status": "approved",
                "aml_alert": aml_alert.id if aml_alert else None
            },
            ip_address="***********"
        )
        
        assert audit_id is not None
        
        print("✅ End-to-end compliance workflow test completed successfully!")
        print(f"   - Authentication: {'✓' if auth_result.success else '✗'}")
        print(f"   - KYC Verification: {'✓' if verified else '✗'}")
        print(f"   - Trading Compliance: {'✓' if compliant else '✗'}")
        print(f"   - AML Monitoring: {'✓' if aml_alert is not None else '✓ (no alert)'}")
        print(f"   - Audit Logging: {'✓' if audit_id else '✗'}")

# Run the comprehensive test suite
async def run_comprehensive_tests():
    """Run all compliance and security tests"""
    print("🧪 Starting A.T.L.A.S. Compliance & Security Integration Tests")
    print("=" * 70)
    
    test_instance = TestComplianceSecurityIntegration()
    
    # Setup engines
    compliance = ComplianceEngine()
    security = SecurityManager("test-jwt-secret-key-12345")
    setup = (compliance, security)
    
    # Run all tests
    tests = [
        test_instance.test_complete_kyc_workflow,
        test_instance.test_aml_transaction_monitoring,
        test_instance.test_audit_trail_logging,
        test_instance.test_authentication_workflow,
        test_instance.test_authorization_rbac,
        test_instance.test_security_event_monitoring,
        test_instance.test_trading_compliance_integration,
        test_instance.test_compliance_reporting,
        test_instance.test_end_to_end_compliance_workflow
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            await test(setup)
            passed += 1
        except Exception as e:
            print(f"❌ Test failed: {test.__name__} - {str(e)}")
            failed += 1
    
    print("\n" + "=" * 70)
    print(f"🎯 Test Results: {passed} passed, {failed} failed")
    print(f"✅ Compliance & Security Framework: {'READY FOR PRODUCTION' if failed == 0 else 'NEEDS ATTENTION'}")
    
    return failed == 0

if __name__ == "__main__":
    asyncio.run(run_comprehensive_tests())
