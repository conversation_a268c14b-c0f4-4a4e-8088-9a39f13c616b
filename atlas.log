2025-06-28 13:17:16 - atlas_server - INFO - atlas_server:74 - Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-06-28 13:17:16 - atlas_server - INFO - atlas_server:75 - Advanced Trading & Learning Analysis System v4.0
2025-06-28 13:17:16 - atlas_server - INFO - atlas_server:76 - Server starting immediately, background initialization in progress...
2025-06-28 13:17:16 - atlas_server - INFO - atlas_server:121 - Starting background initialization...
2025-06-28 13:17:16 - atlas_server - INFO - atlas_server:133 - Importing AtlasOrchestrator...
2025-06-28 13:17:16 - atlas_server - INFO - atlas_server:137 - Initializing AtlasOrchestrator...
2025-06-28 13:21:13 - atlas_server - INFO - atlas_server:74 - Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-06-28 13:21:13 - atlas_server - INFO - atlas_server:75 - Advanced Trading & Learning Analysis System v4.0
2025-06-28 13:21:13 - atlas_server - INFO - atlas_server:76 - Server starting immediately, background initialization in progress...
2025-06-28 13:21:13 - atlas_server - INFO - atlas_server:121 - Starting background initialization...
2025-06-28 13:21:13 - atlas_server - INFO - atlas_server:133 - Importing AtlasOrchestrator...
2025-06-28 13:21:13 - atlas_server - INFO - atlas_server:137 - Initializing AtlasOrchestrator...
2025-06-28 13:21:20 - atlas_ai_engine - INFO - atlas_ai_engine:1065 - Technical Analysis Agent initialized with ML enhancement
2025-06-28 13:21:20 - atlas_ai_engine - INFO - atlas_ai_engine:1228 - Risk Management Agent initialized
2025-06-28 13:21:20 - atlas_ai_engine - INFO - atlas_ai_engine:1374 - Sentiment Analysis Agent initialized with basic sentiment analysis
2025-06-28 13:21:20 - atlas_ai_engine - INFO - atlas_ai_engine:1554 - Execution Agent initialized
2025-06-28 13:21:21 - huggingface_hub.file_download - WARNING - file_download:1719 - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-06-28 13:32:59 - atlas_server - INFO - atlas_server:265 - Predicto processing: hello...
2025-06-28 13:33:05 - httpx - INFO - _client:1740 - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 13:33:31 - atlas_server - INFO - atlas_server:265 - Predicto processing: hello...
2025-06-28 13:33:36 - httpx - INFO - _client:1740 - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 13:34:31 - atlas_server - INFO - atlas_server:265 - Predicto processing: hello...
2025-06-28 13:34:38 - httpx - INFO - _client:1740 - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 13:34:54 - atlas_server - INFO - atlas_server:265 - Predicto processing: make a trade...
2025-06-28 13:34:57 - httpx - INFO - _client:1740 - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
